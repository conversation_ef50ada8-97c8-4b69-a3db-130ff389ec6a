"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PollCommand = void 0;
const BaseCommand_1 = require("../base/BaseCommand");
const errorHandler_1 = require("../../utils/errorHandler");
class PollCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'poll',
            description: 'Create a poll with weighted voting based on coin balance',
            category: BaseCommand_1.CommandCategory.UTILITY,
            guildOnly: true,
            requiredFeatures: ['ECONOMY_SYSTEM']
        });
    }
    customizeCommand(command) {
        command
            .addStringOption(option => option.setName('roles')
            .setDescription('Roles that can vote (mention roles or provide role IDs)')
            .setRequired(true))
            .addStringOption(option => option.setName('title')
            .setDescription('Poll title/question')
            .setRequired(true))
            .addStringOption(option => option.setName('context')
            .setDescription('Poll description/context')
            .setRequired(true))
            .addStringOption(option => option.setName('option1')
            .setDescription('First poll option')
            .setRequired(true))
            .addStringOption(option => option.setName('option2')
            .setDescription('Second poll option')
            .setRequired(true))
            .addStringOption(option => option.setName('option3')
            .setDescription('Third poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option4')
            .setDescription('Fourth poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option5')
            .setDescription('Fifth poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option6')
            .setDescription('Sixth poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option7')
            .setDescription('Seventh poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option8')
            .setDescription('Eighth poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option9')
            .setDescription('Ninth poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option10')
            .setDescription('Tenth poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option11')
            .setDescription('Eleventh poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option12')
            .setDescription('Twelfth poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option13')
            .setDescription('Thirteenth poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option14')
            .setDescription('Fourteenth poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option15')
            .setDescription('Fifteenth poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option16')
            .setDescription('Sixteenth poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option17')
            .setDescription('Seventeenth poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option18')
            .setDescription('Eighteenth poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option19')
            .setDescription('Nineteenth poll option')
            .setRequired(false))
            .addStringOption(option => option.setName('option20')
            .setDescription('Twentieth poll option')
            .setRequired(false));
    }
    setPollService(pollService) {
        this.pollService = pollService;
        this.logger?.info('[PollCommand] PollService injected successfully');
    }
    async executeCommand(context) {
        const { interaction, guild, member } = context;
        try {
            if (!this.pollService) {
                throw new errorHandler_1.ValidationError('Poll service not available - please restart the bot');
            }
            if (!guild || !member) {
                throw new errorHandler_1.ValidationError('This command can only be used in a server');
            }
            const rolesString = interaction.options.getString('roles', true);
            const title = interaction.options.getString('title', true);
            const description = interaction.options.getString('context', true);
            const options = [];
            for (let i = 1; i <= 20; i++) {
                const option = interaction.options.getString(`option${i}`);
                if (option) {
                    options.push(option.trim());
                }
            }
            console.log('🎯 REBUILT POLL COMMAND EXECUTED - New role parsing system active');
            console.log('📊 Poll Command Debug:', {
                guildId: guild?.id,
                userId: interaction.user.id,
                rolesString: rolesString.substring(0, 50) + '...'
            });
            this.logger?.info('[PollCommand] Creating poll', {
                guildId: guild.id,
                createdBy: interaction.user.id,
                title,
                optionCount: options.length,
                rolesString
            });
            const eligibleVoterRoles = this.parseRoles(rolesString, guild);
            await this.validateRolesExist(eligibleVoterRoles, guild);
            const pollConfig = {
                title: title.trim(),
                description: description.trim(),
                options,
                eligibleVoterRoles,
                guildId: guild.id,
                channelId: interaction.channelId,
                createdBy: interaction.user.id
            };
            await interaction.deferReply();
            const initialEmbed = {
                title: '📊 Creating Poll...',
                description: 'Please wait while your poll is being created.',
                color: 0xffff00
            };
            const reply = await interaction.editReply({ embeds: [initialEmbed] });
            const poll = await this.pollService.createPoll(pollConfig, reply.id);
            const { embed, components } = this.pollService.createPollEmbed(poll);
            await interaction.editReply({
                embeds: [embed],
                components
            });
            this.logger?.info('[PollCommand] Poll created successfully', {
                pollId: poll.pollId,
                guildId: guild.id,
                messageId: reply.id,
                totalOptions: poll.options.length
            });
        }
        catch (error) {
            this.logger?.error('[PollCommand] Failed to create poll', {
                error: error instanceof Error ? error.message : 'Unknown error',
                guildId: guild?.id,
                userId: interaction.user.id
            });
            const errorMessage = error instanceof errorHandler_1.ValidationError
                ? error.message
                : 'An error occurred while creating the poll. Please try again.';
            if (interaction.deferred) {
                await interaction.editReply({
                    content: `❌ **Error:** ${errorMessage}`,
                    embeds: [],
                    components: []
                });
            }
            else {
                await interaction.reply({
                    content: `❌ **Error:** ${errorMessage}`,
                    ephemeral: true
                });
            }
        }
    }
    parseRoles(rolesString, guild) {
        const roleIds = [];
        const parts = rolesString.trim().split(/\s+/);
        this.logger?.debug('[PollCommand] Parsing roles', {
            rolesString,
            guildId: guild.id,
            parts
        });
        for (const part of parts) {
            const roleId = part.replace(/[<@&>]/g, '').trim();
            if (/^\d{17,20}$/.test(roleId)) {
                if (!roleIds.includes(roleId)) {
                    roleIds.push(roleId);
                }
            }
            else if (roleId.length > 0) {
                this.logger?.warn('[PollCommand] Invalid role ID format', {
                    originalPart: part,
                    extractedRoleId: roleId,
                    guildId: guild.id
                });
            }
        }
        if (roleIds.length === 0) {
            throw new errorHandler_1.ValidationError('No valid role IDs or mentions found. Please use role mentions (@role) or valid role IDs.');
        }
        this.logger?.debug('[PollCommand] Parsed role IDs', {
            roleIds,
            count: roleIds.length,
            guildId: guild.id
        });
        return roleIds;
    }
    async validateRolesExist(roleIds, guild) {
        const missingRoles = [];
        for (const roleId of roleIds) {
            const role = guild.roles.cache.get(roleId);
            if (!role) {
                missingRoles.push(roleId);
            }
        }
        if (missingRoles.length > 0) {
            this.logger?.warn('[PollCommand] Some roles not found in guild', {
                missingRoles,
                guildId: guild.id
            });
            throw new errorHandler_1.ValidationError(`The following roles were not found in this server: ${missingRoles.join(', ')}. ` +
                'Please ensure all roles exist and try again.');
        }
        this.logger?.debug('[PollCommand] All roles validated successfully', {
            roleIds,
            guildId: guild.id
        });
    }
}
exports.PollCommand = PollCommand;
