"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PollService = void 0;
const discord_js_1 = require("discord.js");
const generateUUID = () => {
    try {
        return generateUUID();
    }
    catch (error) {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
};
const Poll_1 = __importDefault(require("../../models/Poll"));
const features_1 = require("../../config/features");
const errorHandler_1 = require("../../utils/errorHandler");
class PollService {
    constructor(app) {
        this.app = app;
        this.name = 'PollService';
        this.logger = app.logger;
        this.logger.info('[PollService] Poll service initialized with new architecture');
        console.log('🎯 REBUILT POLL SERVICE ACTIVE - New permission validation system loaded');
        console.log('📊 PollService constructor called with new architecture');
        console.log('🔧 This message confirms the rebuilt PollService is being used');
        console.log('🎯 REBUILT POLL SERVICE ACTIVE - New permission validation system loaded');
    }
    async initialize() {
        this.logger.info('[PollService] Service initialized');
    }
    async shutdown() {
        this.logger.info('[PollService] Service shutdown');
    }
    async createPoll(config, messageId) {
        try {
            this.logger.info('[PollService] Creating new poll', {
                title: config.title,
                guildId: config.guildId,
                createdBy: config.createdBy,
                optionCount: config.options.length,
                eligibleRoleCount: config.eligibleVoterRoles.length
            });
            this.validatePollConfig(config);
            const pollData = {
                pollId: generateUUID(),
                guildId: config.guildId,
                channelId: config.channelId,
                messageId: messageId,
                title: config.title,
                description: config.description,
                createdBy: config.createdBy,
                eligibleVoterRoles: config.eligibleVoterRoles,
                options: config.options,
                votes: [],
                totalVotes: 0,
                totalVoteWeight: 0,
                status: 'ACTIVE',
                createdAt: new Date(),
                expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
            };
            const poll = new Poll_1.default(pollData);
            await poll.save();
            this.logger.info('[PollService] Poll created successfully', {
                pollId: poll.pollId,
                guildId: poll.guildId,
                messageId: poll.messageId
            });
            return poll;
        }
        catch (error) {
            this.logger.error('[PollService] Failed to create poll', {
                error: error instanceof Error ? error.message : 'Unknown error',
                config
            });
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            throw new errorHandler_1.DatabaseError('Failed to create poll', error);
        }
    }
    async getPoll(pollId) {
        try {
            const poll = await Poll_1.default.findOne({ pollId });
            if (!poll) {
                this.logger.debug('[PollService] Poll not found', { pollId });
                return null;
            }
            this.logger.debug('[PollService] Poll retrieved', {
                pollId: poll.pollId,
                status: poll.status,
                voteCount: poll.totalVotes
            });
            return poll;
        }
        catch (error) {
            this.logger.error('[PollService] Failed to get poll', {
                error: error instanceof Error ? error.message : 'Unknown error',
                pollId
            });
            throw new errorHandler_1.DatabaseError('Failed to retrieve poll', error);
        }
    }
    async canUserVote(pollId, userId, member) {
        try {
            this.logger.debug('[PollService] Checking vote permissions', {
                pollId,
                userId,
                guildId: member.guild.id
            });
            const poll = await this.getPoll(pollId);
            if (!poll || poll.status !== 'ACTIVE') {
                this.logger.debug('[PollService] Poll not found or not active', {
                    pollId,
                    status: poll?.status
                });
                return false;
            }
            if (!member || !member.roles || !member.roles.cache) {
                this.logger.error('[PollService] Invalid member object', {
                    userId,
                    pollId,
                    hasMember: !!member,
                    hasRoles: !!(member?.roles),
                    hasCache: !!(member?.roles?.cache)
                });
                return false;
            }
            if (member.guild.id !== poll.guildId) {
                this.logger.error('[PollService] Guild mismatch', {
                    userId,
                    pollId,
                    memberGuildId: member.guild.id,
                    pollGuildId: poll.guildId
                });
                return false;
            }
            const userRoleIds = Array.from(member.roles.cache.keys());
            const sanitizedEligibleRoles = poll.eligibleVoterRoles
                .map(roleId => roleId.trim())
                .filter(roleId => /^\d{17,20}$/.test(roleId));
            const validEligibleRoles = sanitizedEligibleRoles;
            if (validEligibleRoles.length === 0) {
                this.logger.error('[PollService] No valid eligible voter roles', {
                    pollId,
                    originalRoles: poll.eligibleVoterRoles,
                    validRoles: validEligibleRoles
                });
                return false;
            }
            const hasEligibleRole = validEligibleRoles.some(roleId => member.roles.cache.has(roleId));
            const userRoleNames = member.roles.cache.map ?
                member.roles.cache.map(r => r.name) :
                Array.from(member.roles.cache.values()).map(r => r.name);
            this.logger.debug('[PollService] Role validation details', {
                pollId,
                userId,
                guildId: member.guild.id,
                eligibleRoles: validEligibleRoles,
                userRoleIds,
                userRoleNames,
                hasEligibleRole
            });
            this.logger.debug('[PollService] Vote permission check result', {
                pollId,
                userId,
                hasEligibleRole,
                eligibleRoles: validEligibleRoles,
                userRoles: userRoleIds
            });
            return hasEligibleRole;
        }
        catch (error) {
            this.logger.error('[PollService] Error checking vote permissions', {
                error: error instanceof Error ? error.message : 'Unknown error',
                pollId,
                userId
            });
            return false;
        }
    }
    async canUserEndPoll(pollId, userId, member) {
        try {
            this.logger.debug('[PollService] Checking end poll permissions', {
                pollId,
                userId,
                guildId: member.guild.id
            });
            const poll = await this.getPoll(pollId);
            if (!poll) {
                this.logger.debug('[PollService] Poll not found for end check', { pollId });
                return false;
            }
            this.logger.debug('[PollService] End permission debug', {
                pollId,
                userId,
                hasAdminPerms: member.permissions.has(discord_js_1.PermissionFlagsBits.Administrator),
                isCreator: poll.createdBy === userId
            });
            if (!member || !member.permissions) {
                this.logger.error('[PollService] Invalid member object for end poll', {
                    userId,
                    pollId,
                    hasMember: !!member,
                    hasPermissions: !!(member?.permissions)
                });
                return false;
            }
            if (member.guild.id !== poll.guildId) {
                this.logger.error('[PollService] Guild mismatch for end poll', {
                    userId,
                    pollId,
                    memberGuildId: member.guild.id,
                    pollGuildId: poll.guildId
                });
                return false;
            }
            const isCreator = poll.createdBy === userId;
            const hasAdminPermissions = member.permissions.has(discord_js_1.PermissionFlagsBits.Administrator);
            const hasManageMessages = member.permissions.has(discord_js_1.PermissionFlagsBits.ManageMessages);
            const canEnd = isCreator || hasAdminPermissions || hasManageMessages;
            console.log('=== POLL END PERMISSION CHECK (NEW SYSTEM) ===');
            console.log('Poll ID:', pollId);
            console.log('User ID:', userId);
            console.log('Poll Creator:', poll.createdBy);
            console.log('Is Creator:', isCreator);
            console.log('Has Administrator:', hasAdminPermissions);
            console.log('Has Manage Messages:', hasManageMessages);
            console.log('Can End Poll:', canEnd);
            console.log('=== END ADMIN CHECK ===');
            this.logger.debug('[PollService] End poll permission check result', {
                pollId,
                userId,
                isCreator,
                hasAdminPermissions,
                hasManageMessages,
                canEnd
            });
            return canEnd;
        }
        catch (error) {
            this.logger.error('[PollService] Error checking end poll permissions', {
                error: error instanceof Error ? error.message : 'Unknown error',
                pollId,
                userId
            });
            return false;
        }
    }
    async castVote(pollId, userId, optionIndex, member) {
        try {
            this.logger.info('[PollService] Casting vote', {
                pollId,
                userId,
                optionIndex,
                guildId: member.guild.id
            });
            const canVote = await this.canUserVote(pollId, userId, member);
            if (!canVote) {
                throw new errorHandler_1.ValidationError('You do not have permission to vote in this poll');
            }
            const poll = await Poll_1.default.findOne({ pollId });
            if (!poll) {
                throw new errorHandler_1.ValidationError('Poll not found');
            }
            if (optionIndex < 0 || optionIndex >= poll.options.length) {
                throw new errorHandler_1.ValidationError('Invalid poll option selected');
            }
            let userBalance = 0;
            try {
                const economyService = this.app.getService('EconomyService');
                if (economyService && typeof economyService.getBalance === 'function') {
                    userBalance = await economyService.getBalance(userId, member.guild.id) || 0;
                }
            }
            catch (error) {
                this.logger.warn('[PollService] Could not get user balance, using default weight', { userId, error });
            }
            const vote = {
                userId,
                optionIndex,
                voteWeight: Math.max(1, userBalance),
                voterDisplayName: member.displayName,
                voterUsername: member.user.username,
                timestamp: new Date()
            };
            poll.addVote(vote);
            await poll.save();
            this.logger.info('[PollService] Vote cast successfully', {
                pollId,
                userId,
                optionIndex,
                voteWeight: vote.voteWeight,
                totalVotes: poll.totalVotes
            });
        }
        catch (error) {
            this.logger.error('[PollService] Failed to cast vote', {
                error: error instanceof Error ? error.message : 'Unknown error',
                pollId,
                userId,
                optionIndex
            });
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            throw new errorHandler_1.DatabaseError('Failed to cast vote', error);
        }
    }
    async endPoll(pollId, userId, member) {
        try {
            this.logger.info('[PollService] Ending poll', {
                pollId,
                userId,
                guildId: member.guild.id
            });
            const canEnd = await this.canUserEndPoll(pollId, userId, member);
            if (!canEnd) {
                throw new errorHandler_1.ValidationError('You do not have permission to end this poll');
            }
            const poll = await Poll_1.default.findOne({ pollId });
            if (!poll) {
                throw new errorHandler_1.ValidationError('Poll not found');
            }
            if (poll.status !== 'ACTIVE') {
                throw new errorHandler_1.ValidationError('Poll is not active');
            }
            poll.endPoll(userId);
            await poll.save();
            this.logger.info('[PollService] Poll ended successfully', {
                pollId,
                endedBy: userId,
                totalVotes: poll.totalVotes,
                totalVoteWeight: poll.totalVoteWeight
            });
            return poll;
        }
        catch (error) {
            this.logger.error('[PollService] Failed to end poll', {
                error: error instanceof Error ? error.message : 'Unknown error',
                pollId,
                userId
            });
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            throw new errorHandler_1.DatabaseError('Failed to end poll', error);
        }
    }
    createPollEmbed(poll) {
        const results = poll.getVoteResults();
        const embed = new discord_js_1.EmbedBuilder()
            .setTitle(`📊 ${poll.title}`)
            .setDescription(poll.description)
            .setColor(poll.status === 'ACTIVE' ? 0x00ff00 : 0xff0000)
            .setTimestamp(poll.createdAt);
        let optionsText = '';
        poll.options.forEach((option, index) => {
            const result = results[index];
            const bar = this.createProgressBar(result.percentage);
            optionsText += `**${index + 1}.** ${option}\n`;
            optionsText += `${bar} ${result.votes} votes (${result.percentage.toFixed(1)}%)\n`;
            optionsText += `💰 ${result.weight} coins (${result.weightPercentage.toFixed(1)}%)\n\n`;
        });
        embed.addFields([
            { name: 'Options', value: optionsText || 'No options available', inline: false },
            { name: 'Total Votes', value: `${poll.totalVotes}`, inline: true },
            { name: 'Total Weight', value: `${poll.totalVoteWeight} coins`, inline: true },
            { name: 'Status', value: poll.status, inline: true }
        ]);
        const components = [];
        if (poll.status === 'ACTIVE') {
            // Discord limits: 5 buttons per row, 5 rows max
            // We can fit up to 20 voting buttons (4 rows of 5) + 1 row for end button
            const maxVotingButtons = Math.min(poll.options.length, 20);
            const buttonsPerRow = 5;
            const maxVotingRows = 4; // Reserve 1 row for end button

            // Create voting button rows
            for (let row = 0; row < maxVotingRows && row * buttonsPerRow < maxVotingButtons; row++) {
                const startIndex = row * buttonsPerRow;
                const endIndex = Math.min(startIndex + buttonsPerRow, maxVotingButtons);

                const rowButtons = poll.options.slice(startIndex, endIndex).map((option, index) => {
                    const optionIndex = startIndex + index;
                    return new discord_js_1.ButtonBuilder()
                        .setCustomId(`poll_vote_${poll.pollId}_${optionIndex}`)
                        .setLabel(`${optionIndex + 1}. ${option.substring(0, 18)}${option.length > 18 ? '...' : ''}`)
                        .setStyle(discord_js_1.ButtonStyle.Primary);
                });

                if (rowButtons.length > 0) {
                    components.push(new discord_js_1.ActionRowBuilder().addComponents(rowButtons));
                }
            }

            // Add end poll button in its own row
            const endButton = new discord_js_1.ButtonBuilder()
                .setCustomId(`poll_end_${poll.pollId}`)
                .setLabel('End Poll')
                .setStyle(discord_js_1.ButtonStyle.Danger);

            components.push(new discord_js_1.ActionRowBuilder().addComponents(endButton));
        }
        return { embed, components };
    }
    createProgressBar(percentage) {
        const barLength = 10;
        const filledLength = Math.round((percentage / 100) * barLength);
        const emptyLength = barLength - filledLength;
        return '█'.repeat(filledLength) + '░'.repeat(emptyLength);
    }
    validatePollConfig(config) {
        if (!config.title || config.title.trim().length === 0) {
            throw new errorHandler_1.ValidationError('Poll title is required');
        }
        if (config.title.length > 256) {
            throw new errorHandler_1.ValidationError('Poll title cannot exceed 256 characters');
        }
        if (!config.description || config.description.trim().length === 0) {
            throw new errorHandler_1.ValidationError('Poll description is required');
        }
        if (config.description.length > 1024) {
            throw new errorHandler_1.ValidationError('Poll description cannot exceed 1024 characters');
        }
        if (!config.options || config.options.length < 2) {
            throw new errorHandler_1.ValidationError('At least 2 poll options are required');
        }
        if (config.options.length > 20) {
            throw new errorHandler_1.ValidationError('Maximum 20 poll options allowed');
        }
        config.options.forEach((option, index) => {
            if (!option || option.trim().length === 0) {
                throw new errorHandler_1.ValidationError(`Poll option ${index + 1} cannot be empty`);
            }
            if (option.length > 100) {
                throw new errorHandler_1.ValidationError(`Poll option ${index + 1} cannot exceed 100 characters`);
            }
        });
        if (!config.eligibleVoterRoles || config.eligibleVoterRoles.length === 0) {
            throw new errorHandler_1.ValidationError('At least one eligible voter role is required');
        }
        config.eligibleVoterRoles.forEach((roleId, index) => {
            const trimmedRoleId = roleId.trim();
            if (!/^\d{17,20}$/.test(trimmedRoleId)) {
                throw new errorHandler_1.ValidationError(`Eligible voter role ${index + 1} has invalid format: ${roleId}. Must be a valid Discord snowflake.`);
            }
        });
        if (!/^\d{17,20}$/.test(config.guildId)) {
            throw new errorHandler_1.ValidationError('Invalid guild ID format');
        }
        if (!/^\d{17,20}$/.test(config.channelId)) {
            throw new errorHandler_1.ValidationError('Invalid channel ID format');
        }
        if (!/^\d{17,20}$/.test(config.createdBy)) {
            throw new errorHandler_1.ValidationError('Invalid creator ID format');
        }
    }
}
exports.PollService = PollService;
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PollService.prototype, "createPoll", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PollService.prototype, "getPoll", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, discord_js_1.GuildMember]),
    __metadata("design:returntype", Promise)
], PollService.prototype, "canUserVote", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, discord_js_1.GuildMember]),
    __metadata("design:returntype", Promise)
], PollService.prototype, "canUserEndPoll", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number, discord_js_1.GuildMember]),
    __metadata("design:returntype", Promise)
], PollService.prototype, "castVote", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, discord_js_1.GuildMember]),
    __metadata("design:returntype", Promise)
], PollService.prototype, "endPoll", null);
